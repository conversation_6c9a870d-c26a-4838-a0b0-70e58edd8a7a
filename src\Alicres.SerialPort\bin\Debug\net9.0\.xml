<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Alicres.SerialPort</name>
    </assembly>
    <members>
        <member name="T:Alicres.SerialPort.Constants.SerialPortConstants">
            <summary>
            串口通讯相关常量定义
            </summary>
        </member>
        <member name="T:Alicres.SerialPort.Constants.SerialPortConstants.Defaults">
            <summary>
            默认配置常量
            </summary>
        </member>
        <member name="F:Alicres.SerialPort.Constants.SerialPortConstants.Defaults.BaudRate">
            <summary>
            默认波特率
            </summary>
        </member>
        <member name="F:Alicres.SerialPort.Constants.SerialPortConstants.Defaults.DataBits">
            <summary>
            默认数据位
            </summary>
        </member>
        <member name="F:Alicres.SerialPort.Constants.SerialPortConstants.Defaults.ReadTimeout">
            <summary>
            默认读取超时时间（毫秒）
            </summary>
        </member>
        <member name="F:Alicres.SerialPort.Constants.SerialPortConstants.Defaults.WriteTimeout">
            <summary>
            默认写入超时时间（毫秒）
            </summary>
        </member>
        <member name="F:Alicres.SerialPort.Constants.SerialPortConstants.Defaults.ReceiveBufferSize">
            <summary>
            默认接收缓冲区大小
            </summary>
        </member>
        <member name="F:Alicres.SerialPort.Constants.SerialPortConstants.Defaults.SendBufferSize">
            <summary>
            默认发送缓冲区大小
            </summary>
        </member>
        <member name="F:Alicres.SerialPort.Constants.SerialPortConstants.Defaults.ReconnectInterval">
            <summary>
            默认重连间隔时间（毫秒）
            </summary>
        </member>
        <member name="F:Alicres.SerialPort.Constants.SerialPortConstants.Defaults.MaxReconnectAttempts">
            <summary>
            默认最大重连次数
            </summary>
        </member>
        <member name="T:Alicres.SerialPort.Constants.SerialPortConstants.ErrorMessages">
            <summary>
            错误消息常量
            </summary>
        </member>
        <member name="F:Alicres.SerialPort.Constants.SerialPortConstants.ErrorMessages.PortNameCannotBeEmpty">
            <summary>
            端口名称不能为空
            </summary>
        </member>
        <member name="F:Alicres.SerialPort.Constants.SerialPortConstants.ErrorMessages.PortNotOpen">
            <summary>
            端口未打开
            </summary>
        </member>
        <member name="F:Alicres.SerialPort.Constants.SerialPortConstants.ErrorMessages.PortAlreadyOpen">
            <summary>
            端口已经打开
            </summary>
        </member>
        <member name="F:Alicres.SerialPort.Constants.SerialPortConstants.ErrorMessages.PortNotExists">
            <summary>
            端口不存在
            </summary>
        </member>
        <member name="F:Alicres.SerialPort.Constants.SerialPortConstants.ErrorMessages.PortInUse">
            <summary>
            端口被占用
            </summary>
        </member>
        <member name="F:Alicres.SerialPort.Constants.SerialPortConstants.ErrorMessages.InvalidBaudRate">
            <summary>
            无效的波特率
            </summary>
        </member>
        <member name="F:Alicres.SerialPort.Constants.SerialPortConstants.ErrorMessages.InvalidDataBits">
            <summary>
            无效的数据位
            </summary>
        </member>
        <member name="F:Alicres.SerialPort.Constants.SerialPortConstants.ErrorMessages.SendDataFailed">
            <summary>
            发送数据失败
            </summary>
        </member>
        <member name="F:Alicres.SerialPort.Constants.SerialPortConstants.ErrorMessages.ReceiveDataFailed">
            <summary>
            接收数据失败
            </summary>
        </member>
        <member name="T:Alicres.SerialPort.Exceptions.SerialPortException">
            <summary>
            串口通讯基础异常类
            </summary>
        </member>
        <member name="P:Alicres.SerialPort.Exceptions.SerialPortException.PortName">
            <summary>
            端口名称
            </summary>
        </member>
        <member name="M:Alicres.SerialPort.Exceptions.SerialPortException.#ctor">
            <summary>
            构造函数
            </summary>
        </member>
        <member name="M:Alicres.SerialPort.Exceptions.SerialPortException.#ctor(System.String)">
            <summary>
            构造函数
            </summary>
            <param name="message">异常消息</param>
        </member>
        <member name="M:Alicres.SerialPort.Exceptions.SerialPortException.#ctor(System.String,System.Exception)">
            <summary>
            构造函数
            </summary>
            <param name="message">异常消息</param>
            <param name="innerException">内部异常</param>
        </member>
        <member name="M:Alicres.SerialPort.Exceptions.SerialPortException.#ctor(System.String,System.String)">
            <summary>
            构造函数
            </summary>
            <param name="message">异常消息</param>
            <param name="portName">端口名称</param>
        </member>
        <member name="M:Alicres.SerialPort.Exceptions.SerialPortException.#ctor(System.String,System.String,System.Exception)">
            <summary>
            构造函数
            </summary>
            <param name="message">异常消息</param>
            <param name="portName">端口名称</param>
            <param name="innerException">内部异常</param>
        </member>
        <member name="T:Alicres.SerialPort.Exceptions.SerialPortConnectionException">
            <summary>
            串口连接异常
            </summary>
        </member>
        <member name="M:Alicres.SerialPort.Exceptions.SerialPortConnectionException.#ctor">
            <summary>
            构造函数
            </summary>
        </member>
        <member name="M:Alicres.SerialPort.Exceptions.SerialPortConnectionException.#ctor(System.String)">
            <summary>
            构造函数
            </summary>
            <param name="message">异常消息</param>
        </member>
        <member name="M:Alicres.SerialPort.Exceptions.SerialPortConnectionException.#ctor(System.String,System.Exception)">
            <summary>
            构造函数
            </summary>
            <param name="message">异常消息</param>
            <param name="innerException">内部异常</param>
        </member>
        <member name="M:Alicres.SerialPort.Exceptions.SerialPortConnectionException.#ctor(System.String,System.String)">
            <summary>
            构造函数
            </summary>
            <param name="message">异常消息</param>
            <param name="portName">端口名称</param>
        </member>
        <member name="M:Alicres.SerialPort.Exceptions.SerialPortConnectionException.#ctor(System.String,System.String,System.Exception)">
            <summary>
            构造函数
            </summary>
            <param name="message">异常消息</param>
            <param name="portName">端口名称</param>
            <param name="innerException">内部异常</param>
        </member>
        <member name="T:Alicres.SerialPort.Exceptions.SerialPortConfigurationException">
            <summary>
            串口配置异常
            </summary>
        </member>
        <member name="M:Alicres.SerialPort.Exceptions.SerialPortConfigurationException.#ctor">
            <summary>
            构造函数
            </summary>
        </member>
        <member name="M:Alicres.SerialPort.Exceptions.SerialPortConfigurationException.#ctor(System.String)">
            <summary>
            构造函数
            </summary>
            <param name="message">异常消息</param>
        </member>
        <member name="M:Alicres.SerialPort.Exceptions.SerialPortConfigurationException.#ctor(System.String,System.Exception)">
            <summary>
            构造函数
            </summary>
            <param name="message">异常消息</param>
            <param name="innerException">内部异常</param>
        </member>
        <member name="M:Alicres.SerialPort.Exceptions.SerialPortConfigurationException.#ctor(System.String,System.String)">
            <summary>
            构造函数
            </summary>
            <param name="message">异常消息</param>
            <param name="portName">端口名称</param>
        </member>
        <member name="M:Alicres.SerialPort.Exceptions.SerialPortConfigurationException.#ctor(System.String,System.String,System.Exception)">
            <summary>
            构造函数
            </summary>
            <param name="message">异常消息</param>
            <param name="portName">端口名称</param>
            <param name="innerException">内部异常</param>
        </member>
        <member name="T:Alicres.SerialPort.Exceptions.SerialPortDataException">
            <summary>
            串口数据传输异常
            </summary>
        </member>
        <member name="M:Alicres.SerialPort.Exceptions.SerialPortDataException.#ctor">
            <summary>
            构造函数
            </summary>
        </member>
        <member name="M:Alicres.SerialPort.Exceptions.SerialPortDataException.#ctor(System.String)">
            <summary>
            构造函数
            </summary>
            <param name="message">异常消息</param>
        </member>
        <member name="M:Alicres.SerialPort.Exceptions.SerialPortDataException.#ctor(System.String,System.Exception)">
            <summary>
            构造函数
            </summary>
            <param name="message">异常消息</param>
            <param name="innerException">内部异常</param>
        </member>
        <member name="M:Alicres.SerialPort.Exceptions.SerialPortDataException.#ctor(System.String,System.String)">
            <summary>
            构造函数
            </summary>
            <param name="message">异常消息</param>
            <param name="portName">端口名称</param>
        </member>
        <member name="M:Alicres.SerialPort.Exceptions.SerialPortDataException.#ctor(System.String,System.String,System.Exception)">
            <summary>
            构造函数
            </summary>
            <param name="message">异常消息</param>
            <param name="portName">端口名称</param>
            <param name="innerException">内部异常</param>
        </member>
        <member name="T:Alicres.SerialPort.Extensions.ServiceCollectionExtensions">
            <summary>
            服务集合扩展方法
            </summary>
        </member>
        <member name="M:Alicres.SerialPort.Extensions.ServiceCollectionExtensions.AddAlicresSerialPort(Microsoft.Extensions.DependencyInjection.IServiceCollection)">
            <summary>
            添加 Alicres 串口服务
            </summary>
            <param name="services">服务集合</param>
            <returns>服务集合</returns>
        </member>
        <member name="M:Alicres.SerialPort.Extensions.ServiceCollectionExtensions.AddAlicresSerialPort(Microsoft.Extensions.DependencyInjection.IServiceCollection,System.Action{Alicres.SerialPort.Extensions.SerialPortServiceOptions})">
            <summary>
            添加 Alicres 串口服务并配置选项
            </summary>
            <param name="services">服务集合</param>
            <param name="configureOptions">配置选项委托</param>
            <returns>服务集合</returns>
        </member>
        <member name="T:Alicres.SerialPort.Extensions.SerialPortServiceOptions">
            <summary>
            串口服务选项
            </summary>
        </member>
        <member name="P:Alicres.SerialPort.Extensions.SerialPortServiceOptions.DefaultConfiguration">
            <summary>
            默认配置
            </summary>
        </member>
        <member name="P:Alicres.SerialPort.Extensions.SerialPortServiceOptions.EnableGlobalErrorHandling">
            <summary>
            是否启用全局错误处理
            </summary>
        </member>
        <member name="P:Alicres.SerialPort.Extensions.SerialPortServiceOptions.EnableVerboseLogging">
            <summary>
            是否启用详细日志记录
            </summary>
        </member>
        <member name="P:Alicres.SerialPort.Extensions.SerialPortServiceOptions.GlobalReconnectOptions">
            <summary>
            全局重连配置
            </summary>
        </member>
        <member name="T:Alicres.SerialPort.Extensions.GlobalReconnectOptions">
            <summary>
            全局重连选项
            </summary>
        </member>
        <member name="P:Alicres.SerialPort.Extensions.GlobalReconnectOptions.EnableAutoReconnect">
            <summary>
            是否启用全局自动重连
            </summary>
        </member>
        <member name="P:Alicres.SerialPort.Extensions.GlobalReconnectOptions.ReconnectInterval">
            <summary>
            重连间隔时间（毫秒）
            </summary>
        </member>
        <member name="P:Alicres.SerialPort.Extensions.GlobalReconnectOptions.MaxReconnectAttempts">
            <summary>
            最大重连次数
            </summary>
        </member>
        <member name="T:Alicres.SerialPort.Interfaces.ISerialPortManager">
            <summary>
            串口管理器接口，用于管理多个串口连接
            </summary>
        </member>
        <member name="P:Alicres.SerialPort.Interfaces.ISerialPortManager.SerialPorts">
            <summary>
            获取所有管理的串口服务
            </summary>
        </member>
        <member name="E:Alicres.SerialPort.Interfaces.ISerialPortManager.SerialPortAdded">
            <summary>
            串口添加事件
            </summary>
        </member>
        <member name="E:Alicres.SerialPort.Interfaces.ISerialPortManager.SerialPortRemoved">
            <summary>
            串口移除事件
            </summary>
        </member>
        <member name="E:Alicres.SerialPort.Interfaces.ISerialPortManager.DataReceived">
            <summary>
            全局数据接收事件
            </summary>
        </member>
        <member name="E:Alicres.SerialPort.Interfaces.ISerialPortManager.StatusChanged">
            <summary>
            全局状态变化事件
            </summary>
        </member>
        <member name="E:Alicres.SerialPort.Interfaces.ISerialPortManager.ErrorOccurred">
            <summary>
            全局错误事件
            </summary>
        </member>
        <member name="M:Alicres.SerialPort.Interfaces.ISerialPortManager.CreateSerialPort(Alicres.SerialPort.Models.SerialPortConfiguration)">
            <summary>
            创建并添加串口服务
            </summary>
            <param name="configuration">串口配置</param>
            <returns>创建的串口服务</returns>
            <exception cref="T:System.ArgumentException">端口已存在时抛出</exception>
            <exception cref="T:Alicres.SerialPort.Exceptions.SerialPortConfigurationException">配置无效时抛出</exception>
        </member>
        <member name="M:Alicres.SerialPort.Interfaces.ISerialPortManager.AddSerialPort(Alicres.SerialPort.Interfaces.ISerialPortService)">
            <summary>
            添加现有的串口服务
            </summary>
            <param name="serialPortService">串口服务实例</param>
            <exception cref="T:System.ArgumentException">端口已存在时抛出</exception>
        </member>
        <member name="M:Alicres.SerialPort.Interfaces.ISerialPortManager.RemoveSerialPort(System.String)">
            <summary>
            移除串口服务
            </summary>
            <param name="portName">端口名称</param>
            <returns>如果成功移除返回 true，否则返回 false</returns>
        </member>
        <member name="M:Alicres.SerialPort.Interfaces.ISerialPortManager.GetSerialPort(System.String)">
            <summary>
            获取指定端口的串口服务
            </summary>
            <param name="portName">端口名称</param>
            <returns>串口服务实例，如果不存在返回 null</returns>
        </member>
        <member name="M:Alicres.SerialPort.Interfaces.ISerialPortManager.ContainsPort(System.String)">
            <summary>
            检查端口是否存在
            </summary>
            <param name="portName">端口名称</param>
            <returns>如果存在返回 true，否则返回 false</returns>
        </member>
        <member name="M:Alicres.SerialPort.Interfaces.ISerialPortManager.OpenAllAsync(System.Threading.CancellationToken)">
            <summary>
            打开所有串口
            </summary>
            <param name="cancellationToken">取消令牌</param>
            <returns>成功打开的端口数量</returns>
        </member>
        <member name="M:Alicres.SerialPort.Interfaces.ISerialPortManager.CloseAllAsync(System.Threading.CancellationToken)">
            <summary>
            关闭所有串口
            </summary>
            <param name="cancellationToken">取消令牌</param>
            <returns>成功关闭的端口数量</returns>
        </member>
        <member name="M:Alicres.SerialPort.Interfaces.ISerialPortManager.GetAllStatus">
            <summary>
            获取所有串口的状态
            </summary>
            <returns>端口状态字典</returns>
        </member>
        <member name="M:Alicres.SerialPort.Interfaces.ISerialPortManager.GetAvailablePorts">
            <summary>
            获取系统中可用的串口列表
            </summary>
            <returns>可用串口名称数组</returns>
        </member>
        <member name="M:Alicres.SerialPort.Interfaces.ISerialPortManager.BroadcastAsync(System.Byte[],System.Threading.CancellationToken)">
            <summary>
            广播数据到所有已连接的串口
            </summary>
            <param name="data">要发送的数据</param>
            <param name="cancellationToken">取消令牌</param>
            <returns>成功发送的端口数量</returns>
        </member>
        <member name="M:Alicres.SerialPort.Interfaces.ISerialPortManager.BroadcastTextAsync(System.String,System.Text.Encoding,System.Threading.CancellationToken)">
            <summary>
            广播文本到所有已连接的串口
            </summary>
            <param name="text">要发送的文本</param>
            <param name="encoding">编码方式</param>
            <param name="cancellationToken">取消令牌</param>
            <returns>成功发送的端口数量</returns>
        </member>
        <member name="T:Alicres.SerialPort.Interfaces.SerialPortAddedEventArgs">
            <summary>
            串口添加事件参数
            </summary>
        </member>
        <member name="P:Alicres.SerialPort.Interfaces.SerialPortAddedEventArgs.SerialPortService">
            <summary>
            添加的串口服务
            </summary>
        </member>
        <member name="M:Alicres.SerialPort.Interfaces.SerialPortAddedEventArgs.#ctor(Alicres.SerialPort.Interfaces.ISerialPortService)">
            <summary>
            构造函数
            </summary>
            <param name="serialPortService">串口服务</param>
        </member>
        <member name="T:Alicres.SerialPort.Interfaces.SerialPortRemovedEventArgs">
            <summary>
            串口移除事件参数
            </summary>
        </member>
        <member name="P:Alicres.SerialPort.Interfaces.SerialPortRemovedEventArgs.PortName">
            <summary>
            移除的端口名称
            </summary>
        </member>
        <member name="M:Alicres.SerialPort.Interfaces.SerialPortRemovedEventArgs.#ctor(System.String)">
            <summary>
            构造函数
            </summary>
            <param name="portName">端口名称</param>
        </member>
        <member name="T:Alicres.SerialPort.Interfaces.ISerialPortService">
            <summary>
            串口通讯服务接口
            </summary>
        </member>
        <member name="P:Alicres.SerialPort.Interfaces.ISerialPortService.Configuration">
            <summary>
            当前串口配置
            </summary>
        </member>
        <member name="P:Alicres.SerialPort.Interfaces.ISerialPortService.Status">
            <summary>
            当前串口状态
            </summary>
        </member>
        <member name="P:Alicres.SerialPort.Interfaces.ISerialPortService.IsConnected">
            <summary>
            是否已连接
            </summary>
        </member>
        <member name="E:Alicres.SerialPort.Interfaces.ISerialPortService.DataReceived">
            <summary>
            数据接收事件
            </summary>
        </member>
        <member name="E:Alicres.SerialPort.Interfaces.ISerialPortService.StatusChanged">
            <summary>
            连接状态变化事件
            </summary>
        </member>
        <member name="E:Alicres.SerialPort.Interfaces.ISerialPortService.ErrorOccurred">
            <summary>
            错误发生事件
            </summary>
        </member>
        <member name="M:Alicres.SerialPort.Interfaces.ISerialPortService.GetAvailablePorts">
            <summary>
            获取系统中可用的串口列表
            </summary>
            <returns>可用串口名称数组</returns>
        </member>
        <member name="M:Alicres.SerialPort.Interfaces.ISerialPortService.Configure(Alicres.SerialPort.Models.SerialPortConfiguration)">
            <summary>
            配置串口参数
            </summary>
            <param name="configuration">串口配置</param>
            <exception cref="T:Alicres.SerialPort.Exceptions.SerialPortConfigurationException">配置无效时抛出</exception>
        </member>
        <member name="M:Alicres.SerialPort.Interfaces.ISerialPortService.OpenAsync(System.Threading.CancellationToken)">
            <summary>
            打开串口连接
            </summary>
            <param name="cancellationToken">取消令牌</param>
            <returns>如果成功打开返回 true，否则返回 false</returns>
            <exception cref="T:Alicres.SerialPort.Exceptions.SerialPortConnectionException">连接失败时抛出</exception>
        </member>
        <member name="M:Alicres.SerialPort.Interfaces.ISerialPortService.CloseAsync(System.Threading.CancellationToken)">
            <summary>
            关闭串口连接
            </summary>
            <param name="cancellationToken">取消令牌</param>
            <returns>如果成功关闭返回 true，否则返回 false</returns>
        </member>
        <member name="M:Alicres.SerialPort.Interfaces.ISerialPortService.SendAsync(System.Byte[],System.Threading.CancellationToken)">
            <summary>
            发送数据
            </summary>
            <param name="data">要发送的数据</param>
            <param name="cancellationToken">取消令牌</param>
            <returns>实际发送的字节数</returns>
            <exception cref="T:Alicres.SerialPort.Exceptions.SerialPortDataException">发送失败时抛出</exception>
        </member>
        <member name="M:Alicres.SerialPort.Interfaces.ISerialPortService.SendTextAsync(System.String,System.Text.Encoding,System.Threading.CancellationToken)">
            <summary>
            发送文本数据
            </summary>
            <param name="text">要发送的文本</param>
            <param name="encoding">编码方式，默认为 UTF-8</param>
            <param name="cancellationToken">取消令牌</param>
            <returns>实际发送的字节数</returns>
            <exception cref="T:Alicres.SerialPort.Exceptions.SerialPortDataException">发送失败时抛出</exception>
        </member>
        <member name="M:Alicres.SerialPort.Interfaces.ISerialPortService.ReadAsync(System.Byte[],System.Int32,System.Int32,System.Threading.CancellationToken)">
            <summary>
            读取数据
            </summary>
            <param name="buffer">接收缓冲区</param>
            <param name="offset">缓冲区偏移量</param>
            <param name="count">要读取的字节数</param>
            <param name="cancellationToken">取消令牌</param>
            <returns>实际读取的字节数</returns>
            <exception cref="T:Alicres.SerialPort.Exceptions.SerialPortDataException">读取失败时抛出</exception>
        </member>
        <member name="M:Alicres.SerialPort.Interfaces.ISerialPortService.ReadAllAvailableAsync(System.Threading.CancellationToken)">
            <summary>
            读取所有可用数据
            </summary>
            <param name="cancellationToken">取消令牌</param>
            <returns>读取到的数据</returns>
            <exception cref="T:Alicres.SerialPort.Exceptions.SerialPortDataException">读取失败时抛出</exception>
        </member>
        <member name="M:Alicres.SerialPort.Interfaces.ISerialPortService.ClearReceiveBuffer">
            <summary>
            清空接收缓冲区
            </summary>
        </member>
        <member name="M:Alicres.SerialPort.Interfaces.ISerialPortService.ClearSendBuffer">
            <summary>
            清空发送缓冲区
            </summary>
        </member>
        <member name="M:Alicres.SerialPort.Interfaces.ISerialPortService.GetBytesToRead">
            <summary>
            获取接收缓冲区中的字节数
            </summary>
            <returns>缓冲区中的字节数</returns>
        </member>
        <member name="M:Alicres.SerialPort.Interfaces.ISerialPortService.GetBytesToWrite">
            <summary>
            获取发送缓冲区中的字节数
            </summary>
            <returns>缓冲区中的字节数</returns>
        </member>
        <member name="T:Alicres.SerialPort.Interfaces.SerialPortDataReceivedEventArgs">
            <summary>
            数据接收事件参数
            </summary>
        </member>
        <member name="P:Alicres.SerialPort.Interfaces.SerialPortDataReceivedEventArgs.Data">
            <summary>
            接收到的数据
            </summary>
        </member>
        <member name="M:Alicres.SerialPort.Interfaces.SerialPortDataReceivedEventArgs.#ctor(Alicres.SerialPort.Models.SerialPortData)">
            <summary>
            构造函数
            </summary>
            <param name="data">接收到的数据</param>
        </member>
        <member name="T:Alicres.SerialPort.Interfaces.SerialPortStatusChangedEventArgs">
            <summary>
            状态变化事件参数
            </summary>
        </member>
        <member name="P:Alicres.SerialPort.Interfaces.SerialPortStatusChangedEventArgs.PreviousState">
            <summary>
            之前的状态
            </summary>
        </member>
        <member name="P:Alicres.SerialPort.Interfaces.SerialPortStatusChangedEventArgs.CurrentState">
            <summary>
            当前状态
            </summary>
        </member>
        <member name="P:Alicres.SerialPort.Interfaces.SerialPortStatusChangedEventArgs.PortName">
            <summary>
            端口名称
            </summary>
        </member>
        <member name="M:Alicres.SerialPort.Interfaces.SerialPortStatusChangedEventArgs.#ctor(System.String,Alicres.SerialPort.Models.SerialPortConnectionState,Alicres.SerialPort.Models.SerialPortConnectionState)">
            <summary>
            构造函数
            </summary>
            <param name="portName">端口名称</param>
            <param name="previousState">之前的状态</param>
            <param name="currentState">当前状态</param>
        </member>
        <member name="T:Alicres.SerialPort.Interfaces.SerialPortErrorEventArgs">
            <summary>
            错误事件参数
            </summary>
        </member>
        <member name="P:Alicres.SerialPort.Interfaces.SerialPortErrorEventArgs.Exception">
            <summary>
            错误异常
            </summary>
        </member>
        <member name="P:Alicres.SerialPort.Interfaces.SerialPortErrorEventArgs.PortName">
            <summary>
            端口名称
            </summary>
        </member>
        <member name="P:Alicres.SerialPort.Interfaces.SerialPortErrorEventArgs.Timestamp">
            <summary>
            错误时间
            </summary>
        </member>
        <member name="M:Alicres.SerialPort.Interfaces.SerialPortErrorEventArgs.#ctor(System.String,System.Exception)">
            <summary>
            构造函数
            </summary>
            <param name="portName">端口名称</param>
            <param name="exception">错误异常</param>
        </member>
        <member name="T:Alicres.SerialPort.Models.SerialPortConfiguration">
            <summary>
            串口配置信息
            </summary>
        </member>
        <member name="P:Alicres.SerialPort.Models.SerialPortConfiguration.PortName">
            <summary>
            端口名称（如 COM1, COM2 等）
            </summary>
        </member>
        <member name="P:Alicres.SerialPort.Models.SerialPortConfiguration.BaudRate">
            <summary>
            波特率
            </summary>
        </member>
        <member name="P:Alicres.SerialPort.Models.SerialPortConfiguration.DataBits">
            <summary>
            数据位
            </summary>
        </member>
        <member name="P:Alicres.SerialPort.Models.SerialPortConfiguration.StopBits">
            <summary>
            停止位
            </summary>
        </member>
        <member name="P:Alicres.SerialPort.Models.SerialPortConfiguration.Parity">
            <summary>
            校验位
            </summary>
        </member>
        <member name="P:Alicres.SerialPort.Models.SerialPortConfiguration.Handshake">
            <summary>
            握手协议
            </summary>
        </member>
        <member name="P:Alicres.SerialPort.Models.SerialPortConfiguration.ReadTimeout">
            <summary>
            读取超时时间（毫秒）
            </summary>
        </member>
        <member name="P:Alicres.SerialPort.Models.SerialPortConfiguration.WriteTimeout">
            <summary>
            写入超时时间（毫秒）
            </summary>
        </member>
        <member name="P:Alicres.SerialPort.Models.SerialPortConfiguration.ReceiveBufferSize">
            <summary>
            接收缓冲区大小
            </summary>
        </member>
        <member name="P:Alicres.SerialPort.Models.SerialPortConfiguration.SendBufferSize">
            <summary>
            发送缓冲区大小
            </summary>
        </member>
        <member name="P:Alicres.SerialPort.Models.SerialPortConfiguration.DtrEnable">
            <summary>
            是否启用数据终端就绪信号
            </summary>
        </member>
        <member name="P:Alicres.SerialPort.Models.SerialPortConfiguration.RtsEnable">
            <summary>
            是否启用请求发送信号
            </summary>
        </member>
        <member name="P:Alicres.SerialPort.Models.SerialPortConfiguration.EnableAutoReconnect">
            <summary>
            是否启用自动重连
            </summary>
        </member>
        <member name="P:Alicres.SerialPort.Models.SerialPortConfiguration.ReconnectInterval">
            <summary>
            重连间隔时间（毫秒）
            </summary>
        </member>
        <member name="P:Alicres.SerialPort.Models.SerialPortConfiguration.MaxReconnectAttempts">
            <summary>
            最大重连次数
            </summary>
        </member>
        <member name="M:Alicres.SerialPort.Models.SerialPortConfiguration.IsValid">
            <summary>
            验证配置是否有效
            </summary>
            <returns>如果配置有效返回 true，否则返回 false</returns>
        </member>
        <member name="M:Alicres.SerialPort.Models.SerialPortConfiguration.CreateDefault(System.String)">
            <summary>
            创建默认配置
            </summary>
            <param name="portName">端口名称</param>
            <returns>默认配置实例</returns>
        </member>
        <member name="T:Alicres.SerialPort.Models.SerialPortData">
            <summary>
            串口数据传输模型
            </summary>
        </member>
        <member name="P:Alicres.SerialPort.Models.SerialPortData.RawData">
            <summary>
            原始字节数据
            </summary>
        </member>
        <member name="P:Alicres.SerialPort.Models.SerialPortData.Length">
            <summary>
            数据长度
            </summary>
        </member>
        <member name="P:Alicres.SerialPort.Models.SerialPortData.Timestamp">
            <summary>
            接收时间戳
            </summary>
        </member>
        <member name="P:Alicres.SerialPort.Models.SerialPortData.PortName">
            <summary>
            端口名称
            </summary>
        </member>
        <member name="P:Alicres.SerialPort.Models.SerialPortData.Direction">
            <summary>
            数据方向（发送/接收）
            </summary>
        </member>
        <member name="M:Alicres.SerialPort.Models.SerialPortData.#ctor">
            <summary>
            构造函数
            </summary>
        </member>
        <member name="M:Alicres.SerialPort.Models.SerialPortData.#ctor(System.Byte[],System.String,Alicres.SerialPort.Models.SerialPortDataDirection)">
            <summary>
            构造函数
            </summary>
            <param name="data">字节数据</param>
            <param name="portName">端口名称</param>
            <param name="direction">数据方向</param>
        </member>
        <member name="M:Alicres.SerialPort.Models.SerialPortData.#ctor(System.String,System.String,System.Text.Encoding,Alicres.SerialPort.Models.SerialPortDataDirection)">
            <summary>
            构造函数
            </summary>
            <param name="text">文本数据</param>
            <param name="portName">端口名称</param>
            <param name="encoding">编码方式</param>
            <param name="direction">数据方向</param>
        </member>
        <member name="M:Alicres.SerialPort.Models.SerialPortData.ToText(System.Text.Encoding)">
            <summary>
            将数据转换为字符串
            </summary>
            <param name="encoding">编码方式，默认为 UTF-8</param>
            <returns>字符串表示</returns>
        </member>
        <member name="M:Alicres.SerialPort.Models.SerialPortData.ToHexString(System.String,System.Boolean)">
            <summary>
            将数据转换为十六进制字符串
            </summary>
            <param name="separator">分隔符，默认为空格</param>
            <param name="uppercase">是否使用大写，默认为 true</param>
            <returns>十六进制字符串</returns>
        </member>
        <member name="M:Alicres.SerialPort.Models.SerialPortData.FromHexString(System.String,System.String,Alicres.SerialPort.Models.SerialPortDataDirection)">
            <summary>
            从十六进制字符串创建数据
            </summary>
            <param name="hexString">十六进制字符串</param>
            <param name="portName">端口名称</param>
            <param name="direction">数据方向</param>
            <returns>串口数据实例</returns>
        </member>
        <member name="P:Alicres.SerialPort.Models.SerialPortData.IsEmpty">
            <summary>
            检查数据是否为空
            </summary>
            <returns>如果数据为空返回 true，否则返回 false</returns>
        </member>
        <member name="M:Alicres.SerialPort.Models.SerialPortData.GetDataCopy">
            <summary>
            获取数据的副本
            </summary>
            <returns>数据副本</returns>
        </member>
        <member name="M:Alicres.SerialPort.Models.SerialPortData.ToString">
            <summary>
            重写 ToString 方法
            </summary>
            <returns>字符串表示</returns>
        </member>
        <member name="T:Alicres.SerialPort.Models.SerialPortDataDirection">
            <summary>
            串口数据方向枚举
            </summary>
        </member>
        <member name="F:Alicres.SerialPort.Models.SerialPortDataDirection.Received">
            <summary>
            接收的数据
            </summary>
        </member>
        <member name="F:Alicres.SerialPort.Models.SerialPortDataDirection.Sent">
            <summary>
            发送的数据
            </summary>
        </member>
        <member name="T:Alicres.SerialPort.Models.SerialPortConnectionState">
            <summary>
            串口连接状态枚举
            </summary>
        </member>
        <member name="F:Alicres.SerialPort.Models.SerialPortConnectionState.Disconnected">
            <summary>
            已断开连接
            </summary>
        </member>
        <member name="F:Alicres.SerialPort.Models.SerialPortConnectionState.Connecting">
            <summary>
            正在连接
            </summary>
        </member>
        <member name="F:Alicres.SerialPort.Models.SerialPortConnectionState.Connected">
            <summary>
            已连接
            </summary>
        </member>
        <member name="F:Alicres.SerialPort.Models.SerialPortConnectionState.Disconnecting">
            <summary>
            正在断开连接
            </summary>
        </member>
        <member name="F:Alicres.SerialPort.Models.SerialPortConnectionState.Error">
            <summary>
            连接错误
            </summary>
        </member>
        <member name="F:Alicres.SerialPort.Models.SerialPortConnectionState.Reconnecting">
            <summary>
            正在重连
            </summary>
        </member>
        <member name="T:Alicres.SerialPort.Models.SerialPortStatus">
            <summary>
            串口状态信息
            </summary>
        </member>
        <member name="P:Alicres.SerialPort.Models.SerialPortStatus.PortName">
            <summary>
            端口名称
            </summary>
        </member>
        <member name="P:Alicres.SerialPort.Models.SerialPortStatus.ConnectionState">
            <summary>
            连接状态
            </summary>
        </member>
        <member name="P:Alicres.SerialPort.Models.SerialPortStatus.IsConnected">
            <summary>
            是否已连接
            </summary>
        </member>
        <member name="P:Alicres.SerialPort.Models.SerialPortStatus.LastConnectedTime">
            <summary>
            最后连接时间
            </summary>
        </member>
        <member name="P:Alicres.SerialPort.Models.SerialPortStatus.LastDisconnectedTime">
            <summary>
            最后断开时间
            </summary>
        </member>
        <member name="P:Alicres.SerialPort.Models.SerialPortStatus.ReconnectAttempts">
            <summary>
            重连次数
            </summary>
        </member>
        <member name="P:Alicres.SerialPort.Models.SerialPortStatus.BytesSent">
            <summary>
            发送字节数
            </summary>
        </member>
        <member name="P:Alicres.SerialPort.Models.SerialPortStatus.BytesReceived">
            <summary>
            接收字节数
            </summary>
        </member>
        <member name="P:Alicres.SerialPort.Models.SerialPortStatus.LastError">
            <summary>
            最后错误信息
            </summary>
        </member>
        <member name="P:Alicres.SerialPort.Models.SerialPortStatus.LastErrorTime">
            <summary>
            最后错误时间
            </summary>
        </member>
        <member name="M:Alicres.SerialPort.Models.SerialPortStatus.Create(System.String,Alicres.SerialPort.Models.SerialPortConnectionState)">
            <summary>
            创建新的状态实例
            </summary>
            <param name="portName">端口名称</param>
            <param name="connectionState">连接状态</param>
            <returns>状态实例</returns>
        </member>
        <member name="M:Alicres.SerialPort.Models.SerialPortStatus.UpdateConnectionState(Alicres.SerialPort.Models.SerialPortConnectionState)">
            <summary>
            更新连接状态
            </summary>
            <param name="newState">新状态</param>
        </member>
        <member name="M:Alicres.SerialPort.Models.SerialPortStatus.RecordError(System.String)">
            <summary>
            记录错误信息
            </summary>
            <param name="errorMessage">错误消息</param>
        </member>
        <member name="M:Alicres.SerialPort.Models.SerialPortStatus.IncrementReconnectAttempts">
            <summary>
            增加重连次数
            </summary>
        </member>
        <member name="M:Alicres.SerialPort.Models.SerialPortStatus.ResetReconnectAttempts">
            <summary>
            重置重连次数
            </summary>
        </member>
        <member name="M:Alicres.SerialPort.Models.SerialPortStatus.UpdateDataStatistics(System.Int64,System.Int64)">
            <summary>
            更新数据统计
            </summary>
            <param name="sentBytes">发送字节数</param>
            <param name="receivedBytes">接收字节数</param>
        </member>
        <member name="T:Alicres.SerialPort.Services.SerialPortManager">
            <summary>
            串口管理器实现
            </summary>
        </member>
        <member name="P:Alicres.SerialPort.Services.SerialPortManager.SerialPorts">
            <summary>
            获取所有管理的串口服务
            </summary>
        </member>
        <member name="E:Alicres.SerialPort.Services.SerialPortManager.SerialPortAdded">
            <summary>
            串口添加事件
            </summary>
        </member>
        <member name="E:Alicres.SerialPort.Services.SerialPortManager.SerialPortRemoved">
            <summary>
            串口移除事件
            </summary>
        </member>
        <member name="E:Alicres.SerialPort.Services.SerialPortManager.DataReceived">
            <summary>
            全局数据接收事件
            </summary>
        </member>
        <member name="E:Alicres.SerialPort.Services.SerialPortManager.StatusChanged">
            <summary>
            全局状态变化事件
            </summary>
        </member>
        <member name="E:Alicres.SerialPort.Services.SerialPortManager.ErrorOccurred">
            <summary>
            全局错误事件
            </summary>
        </member>
        <member name="M:Alicres.SerialPort.Services.SerialPortManager.#ctor(Microsoft.Extensions.Logging.ILogger{Alicres.SerialPort.Services.SerialPortManager},System.IServiceProvider)">
            <summary>
            构造函数
            </summary>
            <param name="logger">日志记录器</param>
            <param name="serviceProvider">服务提供程序</param>
        </member>
        <member name="M:Alicres.SerialPort.Services.SerialPortManager.CreateSerialPort(Alicres.SerialPort.Models.SerialPortConfiguration)">
            <summary>
            创建并添加串口服务
            </summary>
            <param name="configuration">串口配置</param>
            <returns>创建的串口服务</returns>
            <exception cref="T:System.ArgumentException">端口已存在时抛出</exception>
            <exception cref="T:Alicres.SerialPort.Exceptions.SerialPortConfigurationException">配置无效时抛出</exception>
        </member>
        <member name="M:Alicres.SerialPort.Services.SerialPortManager.AddSerialPort(Alicres.SerialPort.Interfaces.ISerialPortService)">
            <summary>
            添加现有的串口服务
            </summary>
            <param name="serialPortService">串口服务实例</param>
            <exception cref="T:System.ArgumentException">端口已存在时抛出</exception>
        </member>
        <member name="M:Alicres.SerialPort.Services.SerialPortManager.RemoveSerialPort(System.String)">
            <summary>
            移除串口服务
            </summary>
            <param name="portName">端口名称</param>
            <returns>如果成功移除返回 true，否则返回 false</returns>
        </member>
        <member name="M:Alicres.SerialPort.Services.SerialPortManager.GetSerialPort(System.String)">
            <summary>
            获取指定端口的串口服务
            </summary>
            <param name="portName">端口名称</param>
            <returns>串口服务实例，如果不存在返回 null</returns>
        </member>
        <member name="M:Alicres.SerialPort.Services.SerialPortManager.ContainsPort(System.String)">
            <summary>
            检查端口是否存在
            </summary>
            <param name="portName">端口名称</param>
            <returns>如果存在返回 true，否则返回 false</returns>
        </member>
        <member name="M:Alicres.SerialPort.Services.SerialPortManager.OpenAllAsync(System.Threading.CancellationToken)">
            <summary>
            打开所有串口
            </summary>
            <param name="cancellationToken">取消令牌</param>
            <returns>成功打开的端口数量</returns>
        </member>
        <member name="M:Alicres.SerialPort.Services.SerialPortManager.CloseAllAsync(System.Threading.CancellationToken)">
            <summary>
            关闭所有串口
            </summary>
            <param name="cancellationToken">取消令牌</param>
            <returns>成功关闭的端口数量</returns>
        </member>
        <member name="M:Alicres.SerialPort.Services.SerialPortManager.GetAllStatus">
            <summary>
            获取所有串口的状态
            </summary>
            <returns>端口状态字典</returns>
        </member>
        <member name="M:Alicres.SerialPort.Services.SerialPortManager.GetAvailablePorts">
            <summary>
            获取系统中可用的串口列表
            </summary>
            <returns>可用串口名称数组</returns>
        </member>
        <member name="M:Alicres.SerialPort.Services.SerialPortManager.BroadcastAsync(System.Byte[],System.Threading.CancellationToken)">
            <summary>
            广播数据到所有已连接的串口
            </summary>
            <param name="data">要发送的数据</param>
            <param name="cancellationToken">取消令牌</param>
            <returns>成功发送的端口数量</returns>
        </member>
        <member name="M:Alicres.SerialPort.Services.SerialPortManager.BroadcastTextAsync(System.String,System.Text.Encoding,System.Threading.CancellationToken)">
            <summary>
            广播文本到所有已连接的串口
            </summary>
            <param name="text">要发送的文本</param>
            <param name="encoding">编码方式</param>
            <param name="cancellationToken">取消令牌</param>
            <returns>成功发送的端口数量</returns>
        </member>
        <member name="M:Alicres.SerialPort.Services.SerialPortManager.Dispose">
            <summary>
            释放资源
            </summary>
        </member>
        <member name="M:Alicres.SerialPort.Services.SerialPortManager.Dispose(System.Boolean)">
            <summary>
            释放资源
            </summary>
            <param name="disposing">是否正在释放</param>
        </member>
        <member name="M:Alicres.SerialPort.Services.SerialPortManager.OnSerialPortDataReceived(System.Object,Alicres.SerialPort.Interfaces.SerialPortDataReceivedEventArgs)">
            <summary>
            串口数据接收事件处理
            </summary>
            <param name="sender">发送者</param>
            <param name="e">事件参数</param>
        </member>
        <member name="M:Alicres.SerialPort.Services.SerialPortManager.OnSerialPortStatusChanged(System.Object,Alicres.SerialPort.Interfaces.SerialPortStatusChangedEventArgs)">
            <summary>
            串口状态变化事件处理
            </summary>
            <param name="sender">发送者</param>
            <param name="e">事件参数</param>
        </member>
        <member name="M:Alicres.SerialPort.Services.SerialPortManager.OnSerialPortErrorOccurred(System.Object,Alicres.SerialPort.Interfaces.SerialPortErrorEventArgs)">
            <summary>
            串口错误事件处理
            </summary>
            <param name="sender">发送者</param>
            <param name="e">事件参数</param>
        </member>
        <member name="M:Alicres.SerialPort.Services.SerialPortManager.OnSerialPortAdded(Alicres.SerialPort.Interfaces.SerialPortAddedEventArgs)">
            <summary>
            触发串口添加事件
            </summary>
            <param name="e">事件参数</param>
        </member>
        <member name="M:Alicres.SerialPort.Services.SerialPortManager.OnSerialPortRemoved(Alicres.SerialPort.Interfaces.SerialPortRemovedEventArgs)">
            <summary>
            触发串口移除事件
            </summary>
            <param name="e">事件参数</param>
        </member>
        <member name="M:Alicres.SerialPort.Services.SerialPortManager.OnDataReceived(Alicres.SerialPort.Interfaces.SerialPortDataReceivedEventArgs)">
            <summary>
            触发数据接收事件
            </summary>
            <param name="e">事件参数</param>
        </member>
        <member name="M:Alicres.SerialPort.Services.SerialPortManager.OnStatusChanged(Alicres.SerialPort.Interfaces.SerialPortStatusChangedEventArgs)">
            <summary>
            触发状态变化事件
            </summary>
            <param name="e">事件参数</param>
        </member>
        <member name="M:Alicres.SerialPort.Services.SerialPortManager.OnErrorOccurred(Alicres.SerialPort.Interfaces.SerialPortErrorEventArgs)">
            <summary>
            触发错误事件
            </summary>
            <param name="e">事件参数</param>
        </member>
        <member name="M:Alicres.SerialPort.Services.SerialPortManager.ThrowIfDisposed">
            <summary>
            检查是否已释放
            </summary>
            <exception cref="T:System.ObjectDisposedException">已释放时抛出</exception>
        </member>
        <member name="T:Alicres.SerialPort.Services.SerialPortService">
            <summary>
            串口通讯服务实现
            </summary>
        </member>
        <member name="P:Alicres.SerialPort.Services.SerialPortService.Configuration">
            <summary>
            当前串口配置
            </summary>
        </member>
        <member name="P:Alicres.SerialPort.Services.SerialPortService.Status">
            <summary>
            当前串口状态
            </summary>
        </member>
        <member name="P:Alicres.SerialPort.Services.SerialPortService.IsConnected">
            <summary>
            是否已连接
            </summary>
        </member>
        <member name="E:Alicres.SerialPort.Services.SerialPortService.DataReceived">
            <summary>
            数据接收事件
            </summary>
        </member>
        <member name="E:Alicres.SerialPort.Services.SerialPortService.StatusChanged">
            <summary>
            连接状态变化事件
            </summary>
        </member>
        <member name="E:Alicres.SerialPort.Services.SerialPortService.ErrorOccurred">
            <summary>
            错误发生事件
            </summary>
        </member>
        <member name="M:Alicres.SerialPort.Services.SerialPortService.#ctor(Microsoft.Extensions.Logging.ILogger{Alicres.SerialPort.Services.SerialPortService})">
            <summary>
            构造函数
            </summary>
            <param name="logger">日志记录器</param>
        </member>
        <member name="M:Alicres.SerialPort.Services.SerialPortService.#ctor(Alicres.SerialPort.Models.SerialPortConfiguration,Microsoft.Extensions.Logging.ILogger{Alicres.SerialPort.Services.SerialPortService})">
            <summary>
            构造函数
            </summary>
            <param name="configuration">串口配置</param>
            <param name="logger">日志记录器</param>
        </member>
        <member name="M:Alicres.SerialPort.Services.SerialPortService.GetAvailablePorts">
            <summary>
            获取系统中可用的串口列表
            </summary>
            <returns>可用串口名称数组</returns>
        </member>
        <member name="M:Alicres.SerialPort.Services.SerialPortService.Configure(Alicres.SerialPort.Models.SerialPortConfiguration)">
            <summary>
            配置串口参数
            </summary>
            <param name="configuration">串口配置</param>
            <exception cref="T:Alicres.SerialPort.Exceptions.SerialPortConfigurationException">配置无效时抛出</exception>
        </member>
        <member name="M:Alicres.SerialPort.Services.SerialPortService.OpenAsync(System.Threading.CancellationToken)">
            <summary>
            打开串口连接
            </summary>
            <param name="cancellationToken">取消令牌</param>
            <returns>如果成功打开返回 true，否则返回 false</returns>
            <exception cref="T:Alicres.SerialPort.Exceptions.SerialPortConnectionException">连接失败时抛出</exception>
        </member>
        <member name="M:Alicres.SerialPort.Services.SerialPortService.CloseAsync(System.Threading.CancellationToken)">
            <summary>
            关闭串口连接
            </summary>
            <param name="cancellationToken">取消令牌</param>
            <returns>如果成功关闭返回 true，否则返回 false</returns>
        </member>
        <member name="M:Alicres.SerialPort.Services.SerialPortService.SendAsync(System.Byte[],System.Threading.CancellationToken)">
            <summary>
            发送数据
            </summary>
            <param name="data">要发送的数据</param>
            <param name="cancellationToken">取消令牌</param>
            <returns>实际发送的字节数</returns>
            <exception cref="T:Alicres.SerialPort.Exceptions.SerialPortDataException">发送失败时抛出</exception>
        </member>
        <member name="M:Alicres.SerialPort.Services.SerialPortService.SendTextAsync(System.String,System.Text.Encoding,System.Threading.CancellationToken)">
            <summary>
            发送文本数据
            </summary>
            <param name="text">要发送的文本</param>
            <param name="encoding">编码方式，默认为 UTF-8</param>
            <param name="cancellationToken">取消令牌</param>
            <returns>实际发送的字节数</returns>
            <exception cref="T:Alicres.SerialPort.Exceptions.SerialPortDataException">发送失败时抛出</exception>
        </member>
        <member name="M:Alicres.SerialPort.Services.SerialPortService.ReadAsync(System.Byte[],System.Int32,System.Int32,System.Threading.CancellationToken)">
            <summary>
            读取数据
            </summary>
            <param name="buffer">接收缓冲区</param>
            <param name="offset">缓冲区偏移量</param>
            <param name="count">要读取的字节数</param>
            <param name="cancellationToken">取消令牌</param>
            <returns>实际读取的字节数</returns>
            <exception cref="T:Alicres.SerialPort.Exceptions.SerialPortDataException">读取失败时抛出</exception>
        </member>
        <member name="M:Alicres.SerialPort.Services.SerialPortService.ReadAllAvailableAsync(System.Threading.CancellationToken)">
            <summary>
            读取所有可用数据
            </summary>
            <param name="cancellationToken">取消令牌</param>
            <returns>读取到的数据</returns>
            <exception cref="T:Alicres.SerialPort.Exceptions.SerialPortDataException">读取失败时抛出</exception>
        </member>
        <member name="M:Alicres.SerialPort.Services.SerialPortService.ClearReceiveBuffer">
            <summary>
            清空接收缓冲区
            </summary>
        </member>
        <member name="M:Alicres.SerialPort.Services.SerialPortService.ClearSendBuffer">
            <summary>
            清空发送缓冲区
            </summary>
        </member>
        <member name="M:Alicres.SerialPort.Services.SerialPortService.GetBytesToRead">
            <summary>
            获取接收缓冲区中的字节数
            </summary>
            <returns>缓冲区中的字节数</returns>
        </member>
        <member name="M:Alicres.SerialPort.Services.SerialPortService.GetBytesToWrite">
            <summary>
            获取发送缓冲区中的字节数
            </summary>
            <returns>缓冲区中的字节数</returns>
        </member>
        <member name="M:Alicres.SerialPort.Services.SerialPortService.Dispose">
            <summary>
            释放资源
            </summary>
        </member>
        <member name="M:Alicres.SerialPort.Services.SerialPortService.Dispose(System.Boolean)">
            <summary>
            释放资源
            </summary>
            <param name="disposing">是否正在释放</param>
        </member>
        <member name="M:Alicres.SerialPort.Services.SerialPortService.ValidateConfiguration(Alicres.SerialPort.Models.SerialPortConfiguration)">
            <summary>
            验证配置
            </summary>
            <param name="configuration">配置</param>
            <exception cref="T:Alicres.SerialPort.Exceptions.SerialPortConfigurationException">配置无效时抛出</exception>
        </member>
        <member name="M:Alicres.SerialPort.Services.SerialPortService.CreateSerialPort">
            <summary>
            创建串口实例
            </summary>
            <returns>串口实例</returns>
        </member>
        <member name="M:Alicres.SerialPort.Services.SerialPortService.CleanupSerialPort">
            <summary>
            清理串口资源
            </summary>
        </member>
        <member name="M:Alicres.SerialPort.Services.SerialPortService.UpdateConnectionState(Alicres.SerialPort.Models.SerialPortConnectionState)">
            <summary>
            更新连接状态
            </summary>
            <param name="newState">新状态</param>
        </member>
        <member name="M:Alicres.SerialPort.Services.SerialPortService.OnDataReceived(System.Object,System.IO.Ports.SerialDataReceivedEventArgs)">
            <summary>
            数据接收事件处理
            </summary>
            <param name="sender">发送者</param>
            <param name="e">事件参数</param>
        </member>
        <member name="M:Alicres.SerialPort.Services.SerialPortService.OnErrorReceived(System.Object,System.IO.Ports.SerialErrorReceivedEventArgs)">
            <summary>
            错误接收事件处理
            </summary>
            <param name="sender">发送者</param>
            <param name="e">事件参数</param>
        </member>
        <member name="M:Alicres.SerialPort.Services.SerialPortService.OnDataReceived(Alicres.SerialPort.Interfaces.SerialPortDataReceivedEventArgs)">
            <summary>
            触发数据接收事件
            </summary>
            <param name="e">事件参数</param>
        </member>
        <member name="M:Alicres.SerialPort.Services.SerialPortService.OnStatusChanged(Alicres.SerialPort.Interfaces.SerialPortStatusChangedEventArgs)">
            <summary>
            触发状态变化事件
            </summary>
            <param name="e">事件参数</param>
        </member>
        <member name="M:Alicres.SerialPort.Services.SerialPortService.OnErrorOccurred(Alicres.SerialPort.Interfaces.SerialPortErrorEventArgs)">
            <summary>
            触发错误事件
            </summary>
            <param name="e">事件参数</param>
        </member>
        <member name="M:Alicres.SerialPort.Services.SerialPortService.OnErrorOccurred(System.Exception)">
            <summary>
            触发错误事件
            </summary>
            <param name="exception">异常</param>
        </member>
        <member name="M:Alicres.SerialPort.Services.SerialPortService.StartReconnectTask">
            <summary>
            开始重连任务
            </summary>
        </member>
        <member name="M:Alicres.SerialPort.Services.SerialPortService.StopReconnectTask">
            <summary>
            停止重连任务
            </summary>
        </member>
        <member name="M:Alicres.SerialPort.Services.SerialPortService.ReconnectAsync(System.Threading.CancellationToken)">
            <summary>
            重连逻辑
            </summary>
            <param name="cancellationToken">取消令牌</param>
        </member>
        <member name="M:Alicres.SerialPort.Services.SerialPortService.ThrowIfDisposed">
            <summary>
            检查是否已释放
            </summary>
            <exception cref="T:System.ObjectDisposedException">已释放时抛出</exception>
        </member>
    </members>
</doc>
