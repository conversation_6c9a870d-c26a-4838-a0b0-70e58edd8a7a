{"format": 1, "restore": {"C:\\Users\\<USER>\\Desktop\\Alicres\\src\\Alicres.SerialPort\\Alicres.SerialPort.csproj": {}}, "projects": {"C:\\Users\\<USER>\\Desktop\\Alicres\\src\\Alicres.SerialPort\\Alicres.SerialPort.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Desktop\\Alicres\\src\\Alicres.SerialPort\\Alicres.SerialPort.csproj", "projectName": "Alicres.SerialPort", "projectPath": "C:\\Users\\<USER>\\Desktop\\Alicres\\src\\Alicres.SerialPort\\Alicres.SerialPort.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Desktop\\Alicres\\src\\Alicres.SerialPort\\obj\\", "projectStyle": "PackageReference", "centralPackageVersionsManagementEnabled": true, "fallbackFolders": ["D:\\Microsoft Visual Studio\\2022\\Shared\\NuGetPackages", "D:\\Syncfusion\\Essential Studio\\WPF\\29.1.33\\ToolboxNuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config", "C:\\Program Files (x86)\\NuGet\\Config\\Syncfusion Toolbox for WPF.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"allWarningsAsErrors": true, "warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": {"target": "Package", "version": "[8.0.0, )", "versionCentrallyManaged": true}, "Microsoft.Extensions.Logging.Abstractions": {"target": "Package", "version": "[8.0.0, )", "versionCentrallyManaged": true}, "Microsoft.Extensions.Options": {"target": "Package", "version": "[8.0.0, )", "versionCentrallyManaged": true}, "System.IO.Ports": {"target": "Package", "version": "[8.0.0, )", "versionCentrallyManaged": true}}, "centralPackageVersions": {"FluentAssertions": "6.12.0", "Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.DependencyInjection": "8.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Hosting": "8.0.0", "Microsoft.Extensions.Logging": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.0", "Microsoft.NET.Test.Sdk": "17.8.0", "Moq": "4.20.69", "System.IO.Ports": "8.0.0", "System.Text.Json": "8.0.0", "xunit": "2.4.2", "xunit.runner.visualstudio": "2.4.5"}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.411/PortableRuntimeIdentifierGraph.json"}}}}}