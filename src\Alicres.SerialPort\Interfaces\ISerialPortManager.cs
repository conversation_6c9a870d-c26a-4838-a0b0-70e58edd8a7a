using Alicres.SerialPort.Models;

namespace Alicres.SerialPort.Interfaces;

/// <summary>
/// 串口管理器接口，用于管理多个串口连接
/// </summary>
public interface ISerialPortManager : IDisposable
{
    /// <summary>
    /// 获取所有管理的串口服务
    /// </summary>
    IReadOnlyDictionary<string, ISerialPortService> SerialPorts { get; }

    /// <summary>
    /// 串口添加事件
    /// </summary>
    event EventHandler<SerialPortAddedEventArgs>? SerialPortAdded;

    /// <summary>
    /// 串口移除事件
    /// </summary>
    event EventHandler<SerialPortRemovedEventArgs>? SerialPortRemoved;

    /// <summary>
    /// 全局数据接收事件
    /// </summary>
    event EventHandler<SerialPortDataReceivedEventArgs>? DataReceived;

    /// <summary>
    /// 全局状态变化事件
    /// </summary>
    event EventHandler<SerialPortStatusChangedEventArgs>? StatusChanged;

    /// <summary>
    /// 全局错误事件
    /// </summary>
    event EventHandler<SerialPortErrorEventArgs>? ErrorOccurred;

    /// <summary>
    /// 创建并添加串口服务
    /// </summary>
    /// <param name="configuration">串口配置</param>
    /// <returns>创建的串口服务</returns>
    /// <exception cref="ArgumentException">端口已存在时抛出</exception>
    /// <exception cref="Exceptions.SerialPortConfigurationException">配置无效时抛出</exception>
    ISerialPortService CreateSerialPort(SerialPortConfiguration configuration);

    /// <summary>
    /// 添加现有的串口服务
    /// </summary>
    /// <param name="serialPortService">串口服务实例</param>
    /// <exception cref="ArgumentException">端口已存在时抛出</exception>
    void AddSerialPort(ISerialPortService serialPortService);

    /// <summary>
    /// 移除串口服务
    /// </summary>
    /// <param name="portName">端口名称</param>
    /// <returns>如果成功移除返回 true，否则返回 false</returns>
    bool RemoveSerialPort(string portName);

    /// <summary>
    /// 获取指定端口的串口服务
    /// </summary>
    /// <param name="portName">端口名称</param>
    /// <returns>串口服务实例，如果不存在返回 null</returns>
    ISerialPortService? GetSerialPort(string portName);

    /// <summary>
    /// 检查端口是否存在
    /// </summary>
    /// <param name="portName">端口名称</param>
    /// <returns>如果存在返回 true，否则返回 false</returns>
    bool ContainsPort(string portName);

    /// <summary>
    /// 打开所有串口
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>成功打开的端口数量</returns>
    Task<int> OpenAllAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// 关闭所有串口
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>成功关闭的端口数量</returns>
    Task<int> CloseAllAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取所有串口的状态
    /// </summary>
    /// <returns>端口状态字典</returns>
    Dictionary<string, SerialPortStatus> GetAllStatus();

    /// <summary>
    /// 获取系统中可用的串口列表
    /// </summary>
    /// <returns>可用串口名称数组</returns>
    string[] GetAvailablePorts();

    /// <summary>
    /// 广播数据到所有已连接的串口
    /// </summary>
    /// <param name="data">要发送的数据</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>成功发送的端口数量</returns>
    Task<int> BroadcastAsync(byte[] data, CancellationToken cancellationToken = default);

    /// <summary>
    /// 广播文本到所有已连接的串口
    /// </summary>
    /// <param name="text">要发送的文本</param>
    /// <param name="encoding">编码方式</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>成功发送的端口数量</returns>
    Task<int> BroadcastTextAsync(string text, System.Text.Encoding? encoding = null, CancellationToken cancellationToken = default);
}

/// <summary>
/// 串口添加事件参数
/// </summary>
public class SerialPortAddedEventArgs : EventArgs
{
    /// <summary>
    /// 添加的串口服务
    /// </summary>
    public ISerialPortService SerialPortService { get; }

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="serialPortService">串口服务</param>
    public SerialPortAddedEventArgs(ISerialPortService serialPortService)
    {
        SerialPortService = serialPortService;
    }
}

/// <summary>
/// 串口移除事件参数
/// </summary>
public class SerialPortRemovedEventArgs : EventArgs
{
    /// <summary>
    /// 移除的端口名称
    /// </summary>
    public string PortName { get; }

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="portName">端口名称</param>
    public SerialPortRemovedEventArgs(string portName)
    {
        PortName = portName;
    }
}
