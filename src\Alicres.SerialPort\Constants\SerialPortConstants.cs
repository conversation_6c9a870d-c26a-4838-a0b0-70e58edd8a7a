namespace Alicres.SerialPort.Constants;

/// <summary>
/// 串口通讯相关常量定义
/// </summary>
public static class SerialPortConstants
{
    /// <summary>
    /// 默认配置常量
    /// </summary>
    public static class Defaults
    {
        /// <summary>
        /// 默认波特率
        /// </summary>
        public const int BaudRate = 9600;

        /// <summary>
        /// 默认数据位
        /// </summary>
        public const int DataBits = 8;

        /// <summary>
        /// 默认读取超时时间（毫秒）
        /// </summary>
        public const int ReadTimeout = 5000;

        /// <summary>
        /// 默认写入超时时间（毫秒）
        /// </summary>
        public const int WriteTimeout = 5000;

        /// <summary>
        /// 默认接收缓冲区大小
        /// </summary>
        public const int ReceiveBufferSize = 4096;

        /// <summary>
        /// 默认发送缓冲区大小
        /// </summary>
        public const int SendBufferSize = 2048;

        /// <summary>
        /// 默认重连间隔时间（毫秒）
        /// </summary>
        public const int ReconnectInterval = 3000;

        /// <summary>
        /// 默认最大重连次数
        /// </summary>
        public const int MaxReconnectAttempts = 5;
    }

    /// <summary>
    /// 错误消息常量
    /// </summary>
    public static class ErrorMessages
    {
        /// <summary>
        /// 端口名称不能为空
        /// </summary>
        public const string PortNameCannotBeEmpty = "端口名称不能为空";

        /// <summary>
        /// 端口未打开
        /// </summary>
        public const string PortNotOpen = "端口未打开";

        /// <summary>
        /// 端口已经打开
        /// </summary>
        public const string PortAlreadyOpen = "端口已经打开";

        /// <summary>
        /// 端口不存在
        /// </summary>
        public const string PortNotExists = "指定的端口不存在";

        /// <summary>
        /// 端口被占用
        /// </summary>
        public const string PortInUse = "端口被其他应用程序占用";

        /// <summary>
        /// 无效的波特率
        /// </summary>
        public const string InvalidBaudRate = "无效的波特率";

        /// <summary>
        /// 无效的数据位
        /// </summary>
        public const string InvalidDataBits = "无效的数据位";

        /// <summary>
        /// 发送数据失败
        /// </summary>
        public const string SendDataFailed = "发送数据失败";

        /// <summary>
        /// 接收数据失败
        /// </summary>
        public const string ReceiveDataFailed = "接收数据失败";
    }
}
