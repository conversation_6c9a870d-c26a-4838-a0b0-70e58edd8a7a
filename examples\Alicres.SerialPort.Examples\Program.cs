using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Alicres.SerialPort.Extensions;
using Alicres.SerialPort.Interfaces;
using Alicres.SerialPort.Models;
using Alicres.SerialPort.Services;

namespace Alicres.SerialPort.Examples;

/// <summary>
/// Alicres.SerialPort 示例程序
/// </summary>
class Program
{
    static async Task Main(string[] args)
    {
        Console.WriteLine("=== Alicres.SerialPort 示例程序 ===\n");

        // 创建主机构建器
        var hostBuilder = Host.CreateDefaultBuilder(args)
            .ConfigureServices((context, services) =>
            {
                // 配置日志
                services.AddLogging(builder =>
                {
                    builder.AddConsole();
                    builder.SetMinimumLevel(LogLevel.Information);
                });

                // 添加 Alicres 串口服务
                services.AddAlicresSerialPort();
            });

        using var host = hostBuilder.Build();
        await host.StartAsync();

        var serviceProvider = host.Services;

        // 显示菜单
        while (true)
        {
            ShowMenu();
            var choice = Console.ReadLine();

            try
            {
                switch (choice)
                {
                    case "1":
                        await BasicSerialPortExample(serviceProvider);
                        break;
                    case "2":
                        await SerialPortManagerExample(serviceProvider);
                        break;
                    case "3":
                        await AutoReconnectExample(serviceProvider);
                        break;
                    case "4":
                        await DataFormatExample(serviceProvider);
                        break;
                    case "5":
                        ListAvailablePorts();
                        break;
                    case "0":
                        Console.WriteLine("退出程序...");
                        return;
                    default:
                        Console.WriteLine("无效选择，请重新输入。");
                        break;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"发生错误: {ex.Message}");
            }

            Console.WriteLine("\n按任意键继续...");
            Console.ReadKey();
            Console.Clear();
        }
    }

    static void ShowMenu()
    {
        Console.WriteLine("请选择示例:");
        Console.WriteLine("1. 基本串口通讯示例");
        Console.WriteLine("2. 串口管理器示例");
        Console.WriteLine("3. 自动重连示例");
        Console.WriteLine("4. 数据格式转换示例");
        Console.WriteLine("5. 列出可用串口");
        Console.WriteLine("0. 退出");
        Console.Write("请输入选择 (0-5): ");
    }

    /// <summary>
    /// 基本串口通讯示例
    /// </summary>
    static async Task BasicSerialPortExample(IServiceProvider serviceProvider)
    {
        Console.WriteLine("\n=== 基本串口通讯示例 ===");

        var logger = serviceProvider.GetRequiredService<ILogger<SerialPortService>>();

        // 获取用户输入的端口名称
        Console.Write("请输入串口名称 (如 COM1): ");
        var portName = Console.ReadLine();
        
        if (string.IsNullOrWhiteSpace(portName))
        {
            Console.WriteLine("端口名称不能为空");
            return;
        }

        // 创建串口配置
        var configuration = new SerialPortConfiguration
        {
            PortName = portName,
            BaudRate = 9600,
            DataBits = 8,
            EnableAutoReconnect = false
        };

        // 创建串口服务
        using var serialPort = new SerialPortService(configuration, logger);

        // 订阅事件
        serialPort.DataReceived += (sender, e) =>
        {
            Console.WriteLine($"[接收] {e.Data.ToText()}");
        };

        serialPort.StatusChanged += (sender, e) =>
        {
            Console.WriteLine($"[状态] {e.PreviousState} -> {e.CurrentState}");
        };

        serialPort.ErrorOccurred += (sender, e) =>
        {
            Console.WriteLine($"[错误] {e.Exception.Message}");
        };

        try
        {
            // 尝试打开串口
            Console.WriteLine($"正在打开串口 {portName}...");
            if (await serialPort.OpenAsync())
            {
                Console.WriteLine("串口打开成功！");
                Console.WriteLine("输入文本发送数据，输入 'quit' 退出:");

                string? input;
                while ((input = Console.ReadLine()) != "quit")
                {
                    if (!string.IsNullOrEmpty(input))
                    {
                        await serialPort.SendTextAsync(input);
                        Console.WriteLine($"[发送] {input}");
                    }
                }
            }
            else
            {
                Console.WriteLine("串口打开失败");
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"操作失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 串口管理器示例
    /// </summary>
    static async Task SerialPortManagerExample(IServiceProvider serviceProvider)
    {
        Console.WriteLine("\n=== 串口管理器示例 ===");

        var serialPortManager = serviceProvider.GetRequiredService<ISerialPortManager>();

        // 订阅全局事件
        serialPortManager.DataReceived += (sender, e) =>
        {
            Console.WriteLine($"[{e.Data.PortName}] 接收: {e.Data.ToText()}");
        };

        serialPortManager.StatusChanged += (sender, e) =>
        {
            Console.WriteLine($"[{e.PortName}] 状态: {e.PreviousState} -> {e.CurrentState}");
        };

        // 创建多个串口配置
        var portNames = new[] { "COM1", "COM2", "COM3" };
        var createdPorts = new List<string>();

        foreach (var portName in portNames)
        {
            try
            {
                var config = SerialPortConfiguration.CreateDefault(portName);
                config.BaudRate = 9600;
                serialPortManager.CreateSerialPort(config);
                createdPorts.Add(portName);
                Console.WriteLine($"创建串口: {portName}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"创建串口 {portName} 失败: {ex.Message}");
            }
        }

        if (createdPorts.Count > 0)
        {
            Console.WriteLine($"\n尝试打开 {createdPorts.Count} 个串口...");
            var openedCount = await serialPortManager.OpenAllAsync();
            Console.WriteLine($"成功打开 {openedCount} 个串口");

            if (openedCount > 0)
            {
                Console.WriteLine("\n输入文本进行广播，输入 'quit' 退出:");
                string? input;
                while ((input = Console.ReadLine()) != "quit")
                {
                    if (!string.IsNullOrEmpty(input))
                    {
                        var broadcastCount = await serialPortManager.BroadcastTextAsync(input);
                        Console.WriteLine($"广播到 {broadcastCount} 个串口");
                    }
                }
            }

            // 关闭所有串口
            var closedCount = await serialPortManager.CloseAllAsync();
            Console.WriteLine($"关闭了 {closedCount} 个串口");
        }
    }

    /// <summary>
    /// 自动重连示例
    /// </summary>
    static async Task AutoReconnectExample(IServiceProvider serviceProvider)
    {
        Console.WriteLine("\n=== 自动重连示例 ===");

        var logger = serviceProvider.GetRequiredService<ILogger<SerialPortService>>();

        Console.Write("请输入串口名称 (如 COM1): ");
        var portName = Console.ReadLine();
        
        if (string.IsNullOrWhiteSpace(portName))
        {
            Console.WriteLine("端口名称不能为空");
            return;
        }

        // 创建启用自动重连的配置
        var configuration = new SerialPortConfiguration
        {
            PortName = portName,
            BaudRate = 9600,
            EnableAutoReconnect = true,
            ReconnectInterval = 2000,      // 2秒重连间隔
            MaxReconnectAttempts = 3       // 最大重连3次
        };

        using var serialPort = new SerialPortService(configuration, logger);

        // 订阅事件
        serialPort.StatusChanged += (sender, e) =>
        {
            Console.WriteLine($"[{DateTime.Now:HH:mm:ss}] 状态变化: {e.PreviousState} -> {e.CurrentState}");
        };

        serialPort.ErrorOccurred += (sender, e) =>
        {
            Console.WriteLine($"[{DateTime.Now:HH:mm:ss}] 错误: {e.Exception.Message}");
        };

        try
        {
            Console.WriteLine("正在尝试连接串口（启用自动重连）...");
            Console.WriteLine("请在连接后拔掉串口设备来测试自动重连功能");
            Console.WriteLine("按 'q' 键退出");

            // 尝试打开串口
            _ = serialPort.OpenAsync();

            // 等待用户输入退出
            while (Console.ReadKey().KeyChar != 'q')
            {
                await Task.Delay(100);
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"操作失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 数据格式转换示例
    /// </summary>
    static async Task DataFormatExample(IServiceProvider serviceProvider)
    {
        Console.WriteLine("\n=== 数据格式转换示例 ===");

        // 演示数据格式转换功能
        var testText = "Hello, 世界!";
        var testBytes = new byte[] { 0x48, 0x65, 0x6C, 0x6C, 0x6F, 0x20, 0xFF, 0x00 };

        Console.WriteLine("1. 文本转换示例:");
        var textData = new SerialPortData(testText, "COM1");
        Console.WriteLine($"   原始文本: {testText}");
        Console.WriteLine($"   转为十六进制: {textData.ToHexString()}");
        Console.WriteLine($"   转回文本: {textData.ToText()}");

        Console.WriteLine("\n2. 字节数组转换示例:");
        var byteData = new SerialPortData(testBytes, "COM1");
        Console.WriteLine($"   原始字节: [{string.Join(", ", testBytes.Select(b => $"0x{b:X2}"))}]");
        Console.WriteLine($"   转为十六进制: {byteData.ToHexString()}");
        Console.WriteLine($"   转为十六进制(小写,冒号分隔): {byteData.ToHexString(":", false)}");

        Console.WriteLine("\n3. 十六进制字符串转换示例:");
        var hexString = "48 65 6C 6C 6F 20 57 6F 72 6C 64";
        Console.WriteLine($"   十六进制字符串: {hexString}");
        var hexData = SerialPortData.FromHexString(hexString, "COM1");
        Console.WriteLine($"   转为文本: {hexData.ToText()}");
        Console.WriteLine($"   字节长度: {hexData.Length}");

        Console.WriteLine("\n4. 数据统计示例:");
        Console.WriteLine($"   数据方向: {textData.Direction}");
        Console.WriteLine($"   时间戳: {textData.Timestamp:yyyy-MM-dd HH:mm:ss.fff}");
        Console.WriteLine($"   是否为空: {textData.IsEmpty}");
        Console.WriteLine($"   ToString(): {textData}");

        await Task.CompletedTask;
    }

    /// <summary>
    /// 列出可用串口
    /// </summary>
    static void ListAvailablePorts()
    {
        Console.WriteLine("\n=== 可用串口列表 ===");

        try
        {
            var ports = System.IO.Ports.SerialPort.GetPortNames();
            
            if (ports.Length > 0)
            {
                Console.WriteLine("系统中可用的串口:");
                foreach (var port in ports)
                {
                    Console.WriteLine($"  - {port}");
                }
            }
            else
            {
                Console.WriteLine("系统中没有可用的串口");
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"获取串口列表失败: {ex.Message}");
        }
    }
}
