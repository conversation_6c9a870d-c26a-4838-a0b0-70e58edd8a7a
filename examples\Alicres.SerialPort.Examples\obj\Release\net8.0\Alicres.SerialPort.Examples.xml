<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Alicres.SerialPort.Examples</name>
    </assembly>
    <members>
        <member name="T:Alicres.SerialPort.Examples.Program">
            <summary>
            Alicres.SerialPort 示例程序
            </summary>
        </member>
        <member name="M:Alicres.SerialPort.Examples.Program.BasicSerialPortExample(System.IServiceProvider)">
            <summary>
            基本串口通讯示例
            </summary>
        </member>
        <member name="M:Alicres.SerialPort.Examples.Program.SerialPortManagerExample(System.IServiceProvider)">
            <summary>
            串口管理器示例
            </summary>
        </member>
        <member name="M:Alicres.SerialPort.Examples.Program.AutoReconnectExample(System.IServiceProvider)">
            <summary>
            自动重连示例
            </summary>
        </member>
        <member name="M:Alicres.SerialPort.Examples.Program.DataFormatExample(System.IServiceProvider)">
            <summary>
            数据格式转换示例
            </summary>
        </member>
        <member name="M:Alicres.SerialPort.Examples.Program.ListAvailablePorts">
            <summary>
            列出可用串口
            </summary>
        </member>
    </members>
</doc>
