﻿<?xml version="1.0" encoding="utf-8"?>
<package xmlns="http://schemas.microsoft.com/packaging/2012/06/nuspec.xsd">
  <metadata>
    <id>Alicres.SerialPort</id>
    <version>1.0.0</version>
    <authors><PERSON><PERSON><PERSON></authors>
    <license type="expression">MIT</license>
    <licenseUrl>https://licenses.nuget.org/MIT</licenseUrl>
    <readme>README.md</readme>
    <projectUrl>https://github.com/alicres/Alicres.SerialPort</projectUrl>
    <description>A comprehensive serial port communication library for .NET applications</description>
    <copyright>Copyright © Alicres 2024</copyright>
    <tags>serialport communication hardware alicres</tags>
    <repository type="git" url="https://github.com/alicres/Alicres.SerialPort" />
    <dependencies>
      <group targetFramework="net8.0">
        <dependency id="Microsoft.Extensions.DependencyInjection.Abstractions" version="8.0.0" exclude="Build,Analyzers" />
        <dependency id="Microsoft.Extensions.Logging.Abstractions" version="8.0.0" exclude="Build,Analyzers" />
        <dependency id="Microsoft.Extensions.Options" version="8.0.0" exclude="Build,Analyzers" />
        <dependency id="System.IO.Ports" version="8.0.0" exclude="Build,Analyzers" />
      </group>
    </dependencies>
  </metadata>
  <files>
    <file src="C:\Users\<USER>\Desktop\Alicres\src\Alicres.SerialPort\bin\Release\net8.0\Alicres.SerialPort.dll" target="lib\net8.0\Alicres.SerialPort.dll" />
    <file src="C:\Users\<USER>\Desktop\Alicres\src\Alicres.SerialPort\bin\Release\net8.0\Alicres.SerialPort.xml" target="lib\net8.0\Alicres.SerialPort.xml" />
    <file src="C:\Users\<USER>\Desktop\Alicres\src\Alicres.SerialPort\README.md" target="\README.md" />
  </files>
</package>