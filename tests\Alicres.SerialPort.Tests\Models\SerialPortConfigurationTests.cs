using System.IO.Ports;
using Alicres.SerialPort.Models;
using FluentAssertions;
using Xunit;

namespace Alicres.SerialPort.Tests.Models;

/// <summary>
/// SerialPortConfiguration 测试类
/// </summary>
public class SerialPortConfigurationTests
{
    [Fact]
    public void Constructor_ShouldInitializeWithDefaultValues()
    {
        // Arrange & Act
        var config = new SerialPortConfiguration();

        // Assert
        config.PortName.Should().BeEmpty();
        config.BaudRate.Should().Be(9600);
        config.DataBits.Should().Be(8);
        config.StopBits.Should().Be(StopBits.One);
        config.Parity.Should().Be(Parity.None);
        config.Handshake.Should().Be(Handshake.None);
        config.ReadTimeout.Should().Be(5000);
        config.WriteTimeout.Should().Be(5000);
        config.ReceiveBufferSize.Should().Be(4096);
        config.SendBufferSize.Should().Be(2048);
        config.DtrEnable.Should().BeFalse();
        config.RtsEnable.Should().BeFalse();
        config.EnableAutoReconnect.Should().BeFalse();
        config.ReconnectInterval.Should().Be(3000);
        config.MaxReconnectAttempts.Should().Be(5);
    }

    [Theory]
    [InlineData("COM1", true)]
    [InlineData("COM2", true)]
    [InlineData("", false)]
    [InlineData(null, false)]
    [InlineData("   ", false)]
    public void IsValid_WithPortName_ShouldReturnExpectedResult(string portName, bool expected)
    {
        // Arrange
        var config = new SerialPortConfiguration
        {
            PortName = portName
        };

        // Act
        var result = config.IsValid();

        // Assert
        result.Should().Be(expected);
    }

    [Theory]
    [InlineData(9600, true)]
    [InlineData(115200, true)]
    [InlineData(0, false)]
    [InlineData(-1, false)]
    public void IsValid_WithBaudRate_ShouldReturnExpectedResult(int baudRate, bool expected)
    {
        // Arrange
        var config = new SerialPortConfiguration
        {
            PortName = "COM1",
            BaudRate = baudRate
        };

        // Act
        var result = config.IsValid();

        // Assert
        result.Should().Be(expected);
    }

    [Theory]
    [InlineData(5, true)]
    [InlineData(6, true)]
    [InlineData(7, true)]
    [InlineData(8, true)]
    [InlineData(4, false)]
    [InlineData(9, false)]
    public void IsValid_WithDataBits_ShouldReturnExpectedResult(int dataBits, bool expected)
    {
        // Arrange
        var config = new SerialPortConfiguration
        {
            PortName = "COM1",
            DataBits = dataBits
        };

        // Act
        var result = config.IsValid();

        // Assert
        result.Should().Be(expected);
    }

    [Theory]
    [InlineData(0, true)]
    [InlineData(1000, true)]
    [InlineData(-1, false)]
    public void IsValid_WithTimeouts_ShouldReturnExpectedResult(int timeout, bool expected)
    {
        // Arrange
        var config = new SerialPortConfiguration
        {
            PortName = "COM1",
            ReadTimeout = timeout,
            WriteTimeout = timeout
        };

        // Act
        var result = config.IsValid();

        // Assert
        result.Should().Be(expected);
    }

    [Theory]
    [InlineData(1024, true)]
    [InlineData(4096, true)]
    [InlineData(0, false)]
    [InlineData(-1, false)]
    public void IsValid_WithBufferSizes_ShouldReturnExpectedResult(int bufferSize, bool expected)
    {
        // Arrange
        var config = new SerialPortConfiguration
        {
            PortName = "COM1",
            ReceiveBufferSize = bufferSize,
            SendBufferSize = bufferSize
        };

        // Act
        var result = config.IsValid();

        // Assert
        result.Should().Be(expected);
    }

    [Theory]
    [InlineData(0, 0, true)]
    [InlineData(3000, 5, true)]
    [InlineData(-1, 0, false)]
    [InlineData(0, -1, false)]
    public void IsValid_WithReconnectSettings_ShouldReturnExpectedResult(int interval, int maxAttempts, bool expected)
    {
        // Arrange
        var config = new SerialPortConfiguration
        {
            PortName = "COM1",
            ReconnectInterval = interval,
            MaxReconnectAttempts = maxAttempts
        };

        // Act
        var result = config.IsValid();

        // Assert
        result.Should().Be(expected);
    }

    [Fact]
    public void CreateDefault_ShouldReturnValidConfiguration()
    {
        // Arrange
        const string portName = "COM1";

        // Act
        var config = SerialPortConfiguration.CreateDefault(portName);

        // Assert
        config.Should().NotBeNull();
        config.PortName.Should().Be(portName);
        config.IsValid().Should().BeTrue();
    }

    [Fact]
    public void CreateDefault_WithEmptyPortName_ShouldReturnInvalidConfiguration()
    {
        // Arrange
        const string portName = "";

        // Act
        var config = SerialPortConfiguration.CreateDefault(portName);

        // Assert
        config.Should().NotBeNull();
        config.PortName.Should().Be(portName);
        config.IsValid().Should().BeFalse();
    }
}
