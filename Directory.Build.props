<Project>
  <PropertyGroup>
    <!-- 目标框架 -->
    <TargetFramework>net9.0</TargetFramework>
    <LangVersion>latest</LangVersion>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>

    <!-- 包信息 -->
    <Authors>Alicres</Authors>
    <Company>Alicres</Company>
    <Copyright>Copyright © Alicres 2024</Copyright>
    <PackageLicenseExpression>MIT</PackageLicenseExpression>
    <RepositoryType>git</RepositoryType>
    <RepositoryUrl>https://github.com/alicres/Alicres.SerialPort</RepositoryUrl>

    <!-- 文档生成 -->
    <GenerateDocumentationFile>true</GenerateDocumentationFile>

    <!-- 代码分析 -->
    <EnableNETAnalyzers>true</EnableNETAnalyzers>
    <AnalysisLevel>latest</AnalysisLevel>
    <TreatWarningsAsErrors>true</TreatWarningsAsErrors>
    <WarningsNotAsErrors>CS1591</WarningsNotAsErrors>

    <!-- 包生成 -->
    <GeneratePackageOnBuild Condition="'$(Configuration)' == 'Release'">true</GeneratePackageOnBuild>
    <IncludeSymbols>true</IncludeSymbols>
    <SymbolPackageFormat>snupkg</SymbolPackageFormat>
  </PropertyGroup>
</Project>
